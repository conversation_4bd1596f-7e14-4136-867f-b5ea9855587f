import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAppSelector, useAppDispatch } from '../../../store/store';
import './performance-card.scss';
import Popup from './pop-up';
import { button } from '../../../utils/strings/pulse-strings';
import { UserDetails } from './interface';
import LineChart from './linechart';
import pulseService from '../../../api/service/pulse';
import {
   formatValue,
   toShowCurrency,
   truncateText,
   getCurrencyThreshold,
} from '../utils/helper';
import { Keys, LocalStorageService } from '../../../utils/local-storage';
import {
   useToast,
   Image,
   Flex,
   Tooltip,
   useColorMode,
   Text,
} from '@chakra-ui/react';
import trackedpin from '../../../assets/icons/tracked-pin.svg';
import { pulseMetaKeys } from '../../dashboard/utils/query-keys';
import { useApiMutation } from '../../../hooks/react-query-hooks';
import {
   calculateHelper,
   specialMetrics,
} from '../../utils/kpiCalculaterHelper';
import { DaywiseCampaignKPIsCalculated } from '../../../api/service/pulse/performance-insights/meta-ads';
import { useQueryClient } from '@tanstack/react-query';
import {
   META_PULSE_OPTIMIZE_PROMPTS,
   META_PULSE_SCALING_PROMPTS,
   MINIMUM_PULSE_DATE_RANGE_DAYS,
   isKPIAllowedForOptimizeScale,
} from '../../marco/utils/analytics-agent/constants';
import {
   setCurrentSessionID,
   setCurrentMode,
   setKpiPrompts,
   setKpiMetadata,
   clearKpiMetadata,
} from '../../../store/reducer/analytics-agent-reducer';

import { getDateRange } from '../utils/helper';

interface CardProps {
   campaign: DaywiseCampaignKPIsCalculated;
   tracked: boolean;
   overview?: string;
   performanceTrackBtn?: string;
   performanceTrackBtnId?: string;
}

const PerformanceCard: React.FC<CardProps> = ({
   campaign,
   performanceTrackBtnId,
   overview,
   tracked,
}) => {
   const toast = useToast();
   const queryClient = useQueryClient();
   const colorMode = useColorMode().colorMode;
   const navigate = useNavigate();
   const dispatch = useAppDispatch();

   const userDetails =
      LocalStorageService.getItem<UserDetails>(Keys.FlableUserDetails) ||
      ({} as UserDetails);

   const { channel, objective, metric } = useAppSelector(
      (state) => state.dropdown,
   );
   const {
      dateRange,
      prevRange,
      currency: kpiCurrency,
   } = useAppSelector((state) => state.kpi);
   const [isPopupOpen, setIsPopupOpen] = useState(false);

   const handlePopupOpen = () => setIsPopupOpen(true);
   const handlePopupClose = () => setIsPopupOpen(false);

   const getKpiDescription = (kpi: string): string => {
      const descriptions: Record<string, string> = {
         roas: 'Return on Ad Spend',
         cpp: 'Cost Per Purchase',
         cpa: 'Cost Per Acquisition',
         leads: 'Lead Generation',
      };
      return descriptions[kpi.toLowerCase()] || kpi.toUpperCase();
   };

   const isDateRangeValid = (): boolean => {
      const { start_date, end_date } = getDateRange(dateRange, prevRange);
      const start = new Date(start_date);
      const end = new Date(end_date);
      const diffTime = Math.abs(end.getTime() - start.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      return diffDays >= MINIMUM_PULSE_DATE_RANGE_DAYS;
   };

   const handleOptimizeClick = async () => {
      if (campaign?.campaign_id) {
         const { start_date, end_date, prev_start_date, prev_end_date } =
            getDateRange(dateRange, prevRange);

         if (!percentage || !arrow) {
            return;
         }

         const isCostBasedKPI = specialMetrics.includes(metric);
         const isUp = direction === 'is up';

         const isGoodPerformance = isCostBasedKPI ? !isUp : isUp;

         const promptSet = isGoodPerformance
            ? META_PULSE_SCALING_PROMPTS
            : META_PULSE_OPTIMIZE_PROMPTS;

         const platformName =
            channel === 'googleads' ? 'Google Ads' : 'Meta Ads';
         const isGoogleAds = channel === 'googleads';
         const normStart = String(start_date).split(/[T\s]/)[0];
         const normEnd = String(end_date).split(/[T\s]/)[0];
         const normPrevStart = String(prev_start_date).split(/[T\s]/)[0];
         const normPrevEnd = String(prev_end_date).split(/[T\s]/)[0];

         const displayPrompt = isGoodPerformance
            ? `Scaling analysis: How can we scale and expand the ${metric} performance for campaign "${campaign.campaign_name}" in ${platformName}?`
            : `Optimization analysis: How can we optimize and improve the ${metric} performance for campaign "${campaign.campaign_name}" in ${platformName} from ${normStart} to ${normEnd} compared to ${normPrevStart} to ${normPrevEnd}?`;

         let kpiPrompt =
            promptSet[metric.toUpperCase() as keyof typeof promptSet];

         if (!kpiPrompt) {
            kpiPrompt = promptSet.ROAS;
         }

         const kpiForTemplate = metric.toUpperCase();

         // Use currency from KPI reducer based on platform (meta or google)
         const platformCurrency = isGoogleAds
            ? kpiCurrency?.google
            : kpiCurrency?.meta;
         const currency =
            String(platformCurrency || '')
               .trim()
               .toUpperCase() || 'INR';

         const currencyThreshold = await getCurrencyThreshold(currency);

         // Normalize all dates to YYYY-MM-DD format
         const normalizedStartDate = String(start_date).split(/[T\s]/)[0];
         const normalizedEndDate = String(end_date).split(/[T\s]/)[0];
         const normalizedPrevStart = String(prev_start_date).split(/[T\s]/)[0];
         const normalizedPrevEnd = String(prev_end_date).split(/[T\s]/)[0];

         const aiPrompt = kpiPrompt
            .replace(/{{campaign_id}}/g, campaign.campaign_id)
            .replace(/{{campaign_name}}/g, campaign.campaign_name)
            .replace(/{{objective}}/g, objective)
            .replace(/{{kpi}}/g, kpiForTemplate)
            .replace(/{{kpi_description}}/g, getKpiDescription(metric))
            .replace(/{{start_date}}/g, normalizedStartDate)
            .replace(/{{end_date}}/g, normalizedEndDate)
            .replace(/{{prev_start_date}}/g, normalizedPrevStart)
            .replace(/{{prev_end_date}}/g, normalizedPrevEnd)
            .replace(/{{currency}}/g, currency)
            .replace(/{{currency_threshold}}/g, currencyThreshold);

         dispatch(setCurrentSessionID(''));
         dispatch(setCurrentMode('cmo'));

         const percentageChange = percentage ? percentage.toString() : '';

         // Determine platform based on channel dropdown and performance
         // Note: Currently only Meta Ads pulse prompts are available, so using META prompts for all channels
         const platform = isGoodPerformance
            ? ('META_PULSE_SCALING_PROMPTS' as const)
            : ('META_PULSE_OPTIMIZE_PROMPTS' as const);

         const kpiMetadata = {
            // Core fields for prompt formation
            kpi: String(metric.toUpperCase()),
            currency: currency,
            currency_threshold: currencyThreshold,
            start_date: normalizedStartDate,
            end_date: normalizedEndDate,
            prev_start_date: normalizedPrevStart,
            prev_end_date: normalizedPrevEnd,
            campaign_name: String(campaign.campaign_name),
            campaign_id: String(campaign.campaign_id),
            percentage_change: String(percentageChange),
            source: isGoodPerformance
               ? ('scale' as const)
               : ('optimize' as const),
            platform: platform,
         };

         dispatch(
            setKpiPrompts({
               displayPrompt,
               aiPrompt,
            }),
         );

         // Clear previous KPI metadata and store new metadata in Redux
         dispatch(clearKpiMetadata());
         dispatch(setKpiMetadata(kpiMetadata));

         navigate('/marco/analytics-agent');
      }
   };

   const handleTrackWrapper = () => {
      void trackedKpiService.mutate({
         client_id: userDetails.client_id,
         kpi_name: metric,
         objective: objective,
         campaign_id: campaign.campaign_id,
         tracked: true,
         channel: channel,
      });
   };

   const handleError = (msg: string | null) =>
      toast({
         title: 'Error',
         description: msg,
         status: 'error',
         duration: 5000,
         isClosable: true,
      });

   const trackedKpiService = useApiMutation({
      mutationFn: pulseService.updateTrackedKpis,
      onSuccessHandler: async () => {
         await queryClient.invalidateQueries({
            queryKey: [pulseMetaKeys.trackedCampaigns],
         });
         toast({
            title: 'Tracked successfully',
            status: 'success',
            duration: 3000,
            isClosable: true,
         });
      },
      onError(msg) {
         handleError(msg);
      },
      invalidateCacheQuery: [pulseMetaKeys.metaCampaigns],
   });

   const getSelectedKPI = () => {
      return (
         campaign.kpis.find((kpi) => kpi.kpi_name === metric) ||
         campaign.kpis[0]
      );
   };

   const filteredChartData = campaign?.kpis.filter(
      (kpi) => !isNaN(Number(kpi.kpi_value)) && kpi.kpi_name === metric,
   );

   const kpi_recommendationData = campaign?.daywise_kpis?.[metric]
      ? Object.entries(campaign.daywise_kpis[metric])
           .filter(([, value]) => value !== null)
           .sort(([, valueA], [, valueB]) => valueB - valueA)
      : [];

   const selectedKPI = getSelectedKPI();
   const selectedKPIValue = campaign?.current_kpi_val?.[metric];
   const prevPeriodKPIValue = campaign?.prev_kpi_val?.[metric];
   const { percentage, color, direction, currentValue } = calculateHelper(
      metric,
      selectedKPIValue,
      prevPeriodKPIValue,
   );
   const arrow = direction === 'is up' ? '↑' : '↓';

   return (
      <div className='CardWrapper'>
         <div className={`usercards ${colorMode}`}>
            <div className={`usercard-containers ${colorMode}`}>
               <div className='card-top'>
                  <div className='left-section'>
                     <Tooltip
                        label={
                           campaign.campaign_name.length > 40
                              ? campaign.campaign_name
                              : ''
                        }
                        placement='top'
                        fontSize='small'
                     >
                        <button className='campaign-name'>
                           <span>
                              {truncateText(campaign.campaign_name, false, 40)}
                           </span>
                        </button>
                     </Tooltip>
                     <button className='campaign-status'>
                        <p
                           className={
                              campaign.recent_campaign_status === 'ACTIVE'
                                 ? 'campaign-status-active'
                                 : 'campaign-status-pause'
                           }
                        >
                           {campaign.recent_campaign_status}
                        </p>
                     </button>
                  </div>
                  {campaign.recent_campaign_status === 'ACTIVE' &&
                     (() => {
                        if (
                           !percentage ||
                           !arrow ||
                           !isDateRangeValid() ||
                           !isKPIAllowedForOptimizeScale(metric) ||
                           Math.abs(parseFloat(percentage)) <= 10
                        ) {
                           return null;
                        }

                        // Determine if this is a cost-based KPI (special metric)
                        const isCostBasedKPI = specialMetrics.includes(metric);
                        const isUp = direction === 'is up';

                        // For cost-based KPIs: up=bad (optimize), down=good (scale)
                        // For revenue-based KPIs: up=good (scale), down=bad (optimize)
                        const shouldScale = isCostBasedKPI ? !isUp : isUp;

                        return (
                           <button
                              className={`optimize-btn ${shouldScale ? 'scale-btn' : 'optimize-btn'}`}
                              onClick={() => {
                                 void handleOptimizeClick();
                              }}
                              title={
                                 shouldScale
                                    ? 'Scale Campaign'
                                    : 'Optimize Campaign'
                              }
                           >
                              <svg
                                 width='16'
                                 height='16'
                                 viewBox='0 0 24 24'
                                 fill='none'
                                 xmlns='http://www.w3.org/2000/svg'
                              >
                                 <path
                                    d='M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z'
                                    fill='#4286f4'
                                 />
                                 <path
                                    d='M19 15L19.74 17.74L22.5 18.5L19.74 19.26L19 22L18.26 19.26L15.5 18.5L18.26 17.74L19 15Z'
                                    fill='#4286f4'
                                 />
                                 <path
                                    d='M5 6L5.47 7.47L7 8L5.47 8.53L5 10L4.53 8.53L3 8L4.53 7.47L5 6Z'
                                    fill='#4286f4'
                                 />
                              </svg>
                              <span>{shouldScale ? 'Scale' : 'Optimize'}</span>
                           </button>
                        );
                     })()}
               </div>
               <div className='chart-elements'>
                  <div className='elements'>
                     <h6 style={{ color }}>
                        <span>{percentage && `${percentage}% ${arrow}`}</span>
                     </h6>
                     <p>
                        <span>
                           {selectedKPI.kpi_name.length > 15 ? (
                              <Tooltip
                                 label={selectedKPI.kpi_name.toUpperCase()}
                                 hasArrow
                                 placement='bottom'
                              >{`${selectedKPI.kpi_name.toUpperCase().slice(0, 15)}...`}</Tooltip>
                           ) : (
                              selectedKPI.kpi_name.toUpperCase()
                           )}{' '}
                           {percentage && direction}
                        </span>
                     </p>
                     <h4>
                        <span>
                           {currentValue !== 'N/A' &&
                              toShowCurrency(metric, campaign?.recent_currency)}
                           {currentValue}
                        </span>
                     </h4>
                  </div>
                  <div className='chart' style={{ color: 'black' }}>
                     {campaign &&
                        filteredChartData &&
                        currentValue !== 'N/A' && (
                           <LineChart
                              kpiDetails={{
                                 displayName: campaign.campaign_name,
                                 allData: filteredChartData.map((kpi) => ({
                                    date: kpi.kpi_date,
                                    kpi_value: formatValue(kpi.kpi_value),
                                 })),
                                 stat: campaign.kpis[0].campaign_status,
                              }}
                           />
                        )}
                  </div>
               </div>
               <div className='kpi-recommendation'>
                  {kpi_recommendationData.length > 1 &&
                     kpi_recommendationData?.[0]?.[1] > 0 && (
                        <>
                           <button className='campaign-recommendation'>
                              <p
                                 className={` ${specialMetrics.includes(metric) ? 'campaign-recommendation-bad-day' : 'campaign-recommendation-good-day'}`}
                              >
                                 {`Highest on ${
                                    kpi_recommendationData?.[0]?.[1] &&
                                    kpi_recommendationData?.[0]?.[0]
                                 }s : ${Math.floor(
                                    kpi_recommendationData?.[0]?.[1] &&
                                       kpi_recommendationData?.[0]?.[1],
                                 )}`}
                              </p>
                           </button>
                           <button className='campaign-recommendation'>
                              <p
                                 className={` ${specialMetrics.includes(metric) ? 'campaign-recommendation-good-day' : 'campaign-recommendation-bad-day'}`}
                              >
                                 {`Lowest on ${
                                    kpi_recommendationData?.[
                                       kpi_recommendationData.length - 1
                                    ]?.[0]
                                 }s : ${Math.ceil(
                                    kpi_recommendationData?.[
                                       kpi_recommendationData.length - 1
                                    ]?.[1],
                                 )}`}
                              </p>
                           </button>
                        </>
                     )}
               </div>
               <div className='bottom'>
                  <hr className={`divider ${colorMode}`} />
                  <div className='bottom-buttons'>
                     {currentValue === 'N/A' || Number(currentValue) === 0 ? (
                        <Tooltip
                           hasArrow
                           label='No campaign available for this campaign'
                        >
                           <Text cursor='not-allowed'>View Details</Text>
                        </Tooltip>
                     ) : (
                        <Link to='#' onClick={handlePopupOpen} id={overview}>
                           View Details
                        </Link>
                     )}

                     <Flex gap={2}>
                        {!tracked && (
                           <Image
                              src={trackedpin}
                              style={{
                                 filter:
                                    colorMode === 'dark' ? 'invert(1)' : 'none',
                              }}
                           />
                        )}
                        <button
                           id={performanceTrackBtnId}
                           className={`track-button ${tracked ? 'tracking' : ''}`}
                           disabled={tracked}
                           style={{
                              cursor: tracked ? 'not-allowed' : 'pointer',
                           }}
                           onClick={handleTrackWrapper}
                        >
                           {tracked ? button.tracking : button.track}
                        </button>
                     </Flex>
                  </div>
               </div>
            </div>
            {isPopupOpen && (
               <Popup
                  campaign={campaign}
                  isOpen={isPopupOpen}
                  onClose={handlePopupClose}
                  details='Detailed information about'
               />
            )}
         </div>
      </div>
   );
};

export default PerformanceCard;
